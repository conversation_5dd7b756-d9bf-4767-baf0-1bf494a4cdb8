// import React from "react";
// const Workflows = () => {
//   return (
//     <div className="p-4">
//         <h1>Workflows</h1>

//     </div>
//   );
// };

// export default Workflows;

import React, { useState } from "react";

const WorkOrderWorkflow = [
  {
    id: 1,
    title: "ELECTRICAL SHUTDOWN JOBS FOR DRIVE DOLVI",
    checklist: "DOLVI CHECKLIST",
    status: "Active",
    date: "14/05/2024",
  },
  {
    id: 2,
    title: "EQUIPMENT INCIDENT REPORT ANUGUL",
    checklist: "ANUGUL CHECKLIST",
    status: "Active",
    date: "21/05/2024",
  },
  {
    id: 3,
    title: "EQUIPMENT INCIDENT REPORT DOLVI",
    checklist: "DOLVI CHECKLIST",
    status: "Active",
    date: "21/05/2024",
  },
  {
    id: 4,
    title: "EQUIPMENT INCIDENT REPORT TORANAGALLU",
    checklist: "TORANAGALLU CHECKLIST",
    status: "Active",
    date: "21/05/2024",
  },
  {
    id: 5,
    title: "EQUIPMENT INCIDENT REPORT TORANAGALLU",
    checklist: "TORANAGALLU CHECKLIST",
    status: "Active",
    date: "21/05/2024",
  },
  {
    id: 6,
    title: "EQUIPMENT INCIDENT REPORT TORANAGALLU",
    checklist: "TORANAGALLU CHECKLIST",
    status: "Active",
    date: "21/05/2024",
  },
  {
    id: 7,
    title: "EQUIPMENT INCIDENT REPORT TORANAGALLU",
    checklist: "TORANAGALLU CHECKLIST",
    status: "Active",
    date: "21/05/2024",
  },
  {
    id: 8,
    title: "EQUIPMENT INCIDENT REPORT TORANAGALLU",
    checklist: "TORANAGALLU CHECKLIST",
    status: "Active",
    date: "21/05/2024",
  },
];

const ScheduleWorkflows = [
  {
    id: 1,
    title: "1Y Preventive maintenance",
    checklist: "TORANAGALLU CHECK",
    status: "Active",
    date: "19/05/2024",
  },
  {
    id: 2,
    title: "2Y Preventive Maintenance DV",
    checklist: "DOLVI CHECKLIST",
    status: "Active",
    date: "11/05/2024",
  },
  {
    id: 3,
    title: "3Y Preventive Maintenance DV",
    checklist: "DOLVI CHECKLIST",
    status: "Active",
    date: "11/05/2024",
  },
  {
    id: 4,
    title: "4Y Preventive Maintenance DV",
    checklist: "DOLVI CHECKLIST",
    status: "Active",
    date: "11/05/2024",
  },
  {
    id: 5,
    title: "5Y Preventive Maintenance DV",
    checklist: "DOLVI CHECKLIST",
    status: "Active",
    date: "11/05/2024",
  },
  {
    id: 6,
    title: "6Y Preventive Maintenance DV",
    checklist: "DOLVI CHECKLIST",
    status: "Active",
    date: "11/05/2024",
  },
];

const Workflows = () => {
  const [activeList, setActiveList] = useState("Work Order Workflow"); // default: list
  const [showPartModal, setShowPartModal] = useState(false);

  // choose which list to show
  const displayedData =
    activeList === "Work Order Workflow"
      ? WorkOrderWorkflow
      : ScheduleWorkflows;

  return (
    <div>
      {/* Navbar */}
      <nav className="bg-gray-100 p-4 shadow-md fixed top-[-14px] left-64 right-0 z-50">
        {/* Row 1 - Heading */}
        <div className="flex justify-between items-center mb-3">
          <h1 className="text-lg font-bold">Workflow</h1>
        </div>

        {/* Row 2 - Buttons + Search Bar */}
        <div className="flex items-center">
          {/* Left buttons */}
          <button
            onClick={() => setActiveList("Work Order Workflow")}
            className={`w-50 border rounded-lg px-4 py-2 bg-white hover:bg-gray-100 focus:outline-none ${
              activeList === "Work Order Workflow"
                ? "ring-2 ring-blue-200 border-blue-400"
                : "border-gray-300"
            }`}
          >
            Work Order Workflow
          </button>
          <button
            onClick={() => setActiveList("Schedule Workflows")}
            className={`w-50 border rounded-lg px-4 py-2 bg-white hover:bg-gray-100 focus:outline-none ${
              activeList === "Schedule Workflows"
                ? "ring-2 ring-blue-200 border-blue-400"
                : "border-gray-300"
            }`}
          >
            Schedule Workflows
          </button>

          {/* Search bar */}
          <div className="relative ml-4">
            <input
              type="text"
              placeholder="Search by Workflow Name"
              className="w-80 border border-gray-300 rounded-lg px-4 py-2 pl-10 focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-blue-400"
            />
            <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
              <i className="fa fa-search"></i>
            </span>
          </div>

          {/* Right-side conditional button */}
          <div className="flex-1 flex justify-end">
            {activeList === "Work Order Workflow" ? (
              <button
                onClick={() => setShowPartModal(true)} // ✅ Open modal
                className="flex items-center space-x-2 bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-700"
              >
                <span className="text-lg">
                  <i className="fas fa-plus-circle pr-1"></i>
                </span>
                <span>Work Order Workflow </span>
              </button>
            ) : (
              <button className="flex items-center space-x-2 bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                <span className="text-lg">
                  <i className="fas fa-plus-circle pr-1"></i>
                </span>
                <span>Schedule Workflow</span>
              </button>
            )}
          </div>
        </div>
      </nav>

      <div className="pt-20 p-4 space-y-4">
        {displayedData.map((workflow, index) => (
          <div
            key={index}
            className="flex items-center justify-between bg-white shadow rounded-lg p-4 border"
          >
            {/* Left image placeholder */}
            <div className="w-12 h-10 bg-gray-200 rounded flex items-center justify-center">
              <span className="text-gray-400 text-sm">Img</span>
            </div>

            {/* Middle content */}
            <div className="flex-1 px-4">
              <p className="font-semibold">{workflow.title}</p>
              <p className="text-gray-700 text-sm">{workflow.checklist}</p>
              <div className="flex items-center mt-2 text-sm text-gray-600">
                {/* <span className="mr-2">📄 Workflow #{workflow.id}</span> */}
              </div>
            </div>

            {/* Right side status & date */}
            <div className="text-right">
              <span
                className={`px-3 py-1 text-sm font-medium rounded-full ${
                  workflow.status === "Active"
                    ? "bg-green-100 text-green-600"
                    : "bg-red-100 text-red-600"
                }`}
              >
                {workflow.status}
              </span>
              <p className="text-xs text-gray-500 mt-1">{workflow.date}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Workflows;
