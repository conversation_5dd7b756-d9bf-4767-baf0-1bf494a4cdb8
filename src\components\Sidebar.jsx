import React from "react";
import { Link } from "react-router-dom";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Fa<PERSON><PERSON><PERSON><PERSON>ist, FaUser<PERSON>og } from "react-icons/fa";
import logo from "../assets/dci_logo.jpg";


const Sidebar = () => {
  return (
    <aside className="w-64 bg-white shadow-md flex flex-col">
      <div className="flex items-center justify-center h-16 border-b">
        <img src={logo} alt="Logo" className="h-10" />
      </div>
      <nav className="flex-1 p-4 space-y-2">
        <Link to="/" className="flex items-center p-2 rounded-lg hover:bg-blue-100">
          <FaChartPie className="mr-3 text-blue-600" /> Dashboard
        </Link>
        <Link to="/inventory-management" className="flex items-center p-2 rounded-lg hover:bg-blue-100">
          <FaClipboardList className="mr-3 text-blue-600" /> Inventory Management
        </Link>
        <Link to="/workflow-automation" className="flex items-center p-2 rounded-lg hover:bg-blue-100">
          <FaCogs className="mr-3 text-blue-600" /> Workflow Automation
        </Link>
        
        <Link to="/login" className="flex items-center p-2 rounded-lg hover:bg-blue-100">
          <FaUserCog className="mr-3 text-blue-600" /> Login
        </Link>
      </nav>
    </aside>
  );
};

export default Sidebar;
