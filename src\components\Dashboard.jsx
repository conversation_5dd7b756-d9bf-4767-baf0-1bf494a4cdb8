import React, { useState } from "react";
import {
  <PERSON>a<PERSON><PERSON><PERSON><PERSON>,
  FaClipboardList,
  FaCogs,
  FaBox,
  FaUserCog,
  FaRegCalendar,
  FaRegCalendarAlt,
} from "react-icons/fa";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  LabelList,
} from "recharts";

import logo from "../assets/dci_logo.jpg";
import { Link } from "react-router-dom";
import Sidebar from "./Sidebar";
import Plot from "react-plotly.js";
import indiaStates from "../assets/india.jpg";
import Calendar from "react-calendar";
import "react-calendar/dist/Calendar.css";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";

// Remove the problematic CSS imports - FullCalendar will work without them

const Dashboard = () => {
  const [selectedView, setSelectedView] = useState("chart");
  const [showFullCalendar, setShowFullCalendar] = useState(false);
  const [date, setDate] = useState(new Date());

  const data = [
    { name: "Over Due", value: 40 },
    { name: "WIP", value: 300 },
    { name: "Pending With Reason", value: 300 },
    { name: "Closed", value: 200 },
    { name: "Rejected", value: 200 },
  ];

  // Define your custom color mapping
  const COLORS = {
    "Over Due": "#FF8042",
    WIP: "#0088FE",
    "Pending With Reason": "#ef717f",
    Closed: "#00C49F",
    Rejected: "#FB4141",
  };

  const tableData = [
    { type: "Daily", overdue: 2, pending: 0, wip: 0, closed: 35, total: 50 },
    { type: "Weekly", overdue: 0, pending: 0, wip: 0, closed: 25, total: 28 },
    { type: "Fortnight", overdue: 0, pending: 0, wip: 0, closed: 0, total: 0 },
    { type: "Monthly", overdue: 0, pending: 0, wip: 0, closed: 88, total: 8 },
    { type: "Bi Monthly", overdue: 0, pending: 0, wip: 0, closed: 0, total: 0 },
    { type: "Quarterly", overdue: 0, pending: 0, wip: 0, closed: 0, total: 0 },
    {
      type: "Quadrimester",
      overdue: 0,
      pending: 0,
      wip: 0,
      closed: 0,
      total: 0,
    },
    {
      type: "Half Yearly",
      overdue: 0,
      pending: 0,
      wip: 0,
      closed: 0,
      total: 0,
    },
    { type: "Yearly", overdue: 0, pending: 0, wip: 0, closed: 0, total: 0 },
    { type: "Bi Yearly", overdue: 0, pending: 0, wip: 0, closed: 0, total: 0 },
    { type: "ADHOC", overdue: 0, pending: 0, wip: 0, closed: 0, total: 0 },
  ];

  const summaryData = [
    { name: "ELECTRICAL - DAILY CHECKLIST FOR …", count: 135 },
    { name: "SHIFT INSPECTION REPORT TORANAGALLU", count: 118 },
    { name: "ELECTRICAL - DAILY CHECKLIST FOR …", count: 36 },
    { name: "SHIFT INSPECTION REPORT DOLVI", count: 36 },
    { name: "DAILY MECHANICAL ANUGUL CHECK LIST", count: 18 },
    { name: "WEEKLY INSPECTION MECHANICAL …", count: 17 },
    { name: "DAILY ELECTRICAL ANUGUL CHECK LIST", count: 16 },
    { name: "MONTHLY INSPECTION MECHANICAL …", count: 5 },
    { name: "WEEKLY INSPECTION REPORT DV", count: 5 },
    { name: "EQUIPMENT INCIDENT REPORT …", count: 5 },
  ];

  const efficiencyData = [
    {
      name: "ASHISH",
      assigned: 282,
      pending: 0,
      rejected: 0,
      completed: 282,
      overdue: 0,
      time: "8 Hour(s) and 19 Min(s)",
      progress: 100,
    },
    {
      name: "ASHUTOSH",
      assigned: 83,
      pending: 0,
      rejected: 0,
      completed: 83,
      overdue: 0,
      time: "3 Hour(s) and 56 Min(s)",
      progress: 100,
    },
    {
      name: "Atanu Narayan",
      assigned: 42,
      pending: 0,
      rejected: 0,
      completed: 37,
      overdue: 5,
      time: "85 Hour(s) and 50 Min(s)",
      progress: 88.1,
    },
    {
      name: "ANSHUMAN",
      assigned: 3,
      pending: 0,
      rejected: 0,
      completed: 0,
      overdue: 3,
      time: "0 Hour(s) and 0 Min(s)",
      progress: 0,
    },
    {
      name: "AMIT",
      assigned: 3,
      pending: 0,
      rejected: 0,
      completed: 0,
      overdue: 3,
      time: "0 Hour(s) and 0 Min(s)",
      progress: 0,
    },
  ];

  const stateData = [
    { state: "Odisha", value: 47 },
    { state: "Maharashtra", value: 113 },
    { state: "Karnataka", value: 290 },
  ];

  const geodata = [
    {
      type: "choroplethmapbox",
      geojson: indiaStates,
      locations: stateData.map((d) => d.state),
      z: stateData.map((d) => d.value),
      colorscale: "Blues",
      marker: { line: { width: 0.5, color: "white" } },
      showscale: false,
      featureidkey: "properties.st_nm", // ✅ matches state name property in geojson
      colorbar: { title: "Closed" },
      opacity: 0.9, // ✅ makes colors bold
    },
  ];

  const layout = {
    mapbox: {
      style: "carto-positron",
      center: { lon: 78.9629, lat: 22.5937 }, // center India
      zoom: 3.8,
    },
    margin: { t: 0, r: 200, b: 0, l: 0 },
    autosize: true,
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Main Content */}
      <main className="flex-1 p-6 overflow-y-auto">
        {/* Navbar */}
        <div className="bg-gray-100 p-4 shadow-md fixed top-[-7px] left-64 right-0 z-50 flex justify-between items-center">
          {/* Title */}
          <h1 className="text-xl font-bold text-gray-800">Dashboard</h1>

          {/* Right Side (Calendar + Dropdown) */}
          <div className="flex items-center space-x-4">
            {/* Calendar Icon */}
            <FaRegCalendarAlt
              className="text-blue-600 w-6 h-6 cursor-pointer hover:text-blue-800"
              onClick={() => setShowFullCalendar(!showFullCalendar)} // toggle calendar
            />

            {/* Dropdown with floating label */}
            <div className="relative">
              <label className="absolute -top-2 left-2 bg-white px-1 text-xs text-blue-600">
                Duration
              </label>
              <select className="appearance-none border rounded-md px-4 py-2 pr-8 focus:outline-none">
                <option>This Month</option>
                <option>Last Month</option>
                <option>Today</option>
                <option>Yesterday</option>
                <option>Custom</option>
              </select>
              {/* Custom arrow */}
              <span className="absolute right-2 top-1/2 -translate-y-1/2 pointer-events-none text-gray-500">
                ▼
              </span>
            </div>
          </div>
        </div>

        {/* Calendar Section (appears below navbar) */}
        {/* {showCalendar && (
          <div className="mt-20 bg-white shadow-lg rounded-lg p-6">
            <Calendar onChange={setDate} value={date} />
          </div>
        )} */}
        {/* If full calendar mode → show only calendar */}
        {/* {showFullCalendar ? (
  <div className="mt-20 bg-white shadow-lg rounded-lg p-6">
    <Calendar onChange={setDate} value={date} />
    <button
      className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md"
      onClick={() => setShowFullCalendar(false)} // go back to dashboard
    >
      Back to Dashboard
    </button>
  </div>
) : (
  <>

  </>
)} */}
        {showFullCalendar ? (
          <div className="mt-20 bg-white shadow-lg rounded-lg p-6">
            <FullCalendar
              plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
              initialView="dayGridMonth"
              selectable={true}
              editable={true}
              events={[
                { title: "Meeting", date: "2025-09-02" },
                { title: "Deadline", date: "2025-09-05" },
                { title: "Review", date: "2025-09-10" },
              ]}
              eventClick={(info) => alert(`Event: ${info.event.title}`)}
              dateClick={(info) => alert(`Clicked on: ${info.dateStr}`)}
            />
            <button
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md"
              onClick={() => setShowFullCalendar(false)}
            >
              Back to Dashboard
            </button>
          </div>
        ) : (
          <>{/* Your existing dashboard content here */}</>
        )}

        {/* Chart Section */}
        <div className="bg-white shadow-lg rounded-xl p-6 mt-10">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-bold text-gray-700 capitalize">
              {selectedView}
            </h2>
            <select
              value={selectedView}
              onChange={(e) => setSelectedView(e.target.value)}
              className="border rounded-lg px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-400"
            >
              <option value="chart">Chart View</option>
              <option value="Line Graph">Line Graph</option>
              <option value="Donut Graph">Donut Graph</option>
            </select>
          </div>

          {/* Conditionally render based on dropdown  bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-700 */}
          {selectedView === "chart" && (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              <div className="bg-gray-200 p-4 rounded-xl hover:bg-blue-200 shadow text-center flex flex-col justify-center items-center">
                <h3 className="text-gray-500">Total</h3>
                <p className="text-2xl font-bold text-black-100">0</p>
              </div>
              <div className="bg-gray-200 p-4 rounded-xl hover:bg-violet-200 shadow text-center flex flex-col justify-center items-center">
                <h3 className="text-gray-500 font-medium text-sm">
                  Work In Progress
                </h3>
                <p className="text-2xl font-bold text-black-400">0</p>
              </div>
              <div className="bg-gray-200 p-4 rounded-xl hover:bg-yellow-100 shadow text-center flex flex-col justify-center items-center">
                <h3 className="text-gray-500 font-medium text-sm">
                  Pending With Progress
                </h3>
                <p className="text-2xl font-bold text-black-400">0</p>
              </div>
              <div className="bg-gray-200 p-4 rounded-xl hover:bg-green-100 shadow text-center flex flex-col justify-center items-center">
                <h3 className="text-gray-500 font-medium text-sm">
                  Closed-Completed
                </h3>
                <p className="text-2xl font-bold text-black-400">0</p>
              </div>
              <div className="bg-gray-200 p-4 rounded-xl hover:bg-orange-100 shadow text-center flex flex-col justify-center items-center">
                <h3 className="text-gray-500 font-medium text-sm">
                  Closed-Incomplete
                </h3>
                <p className="text-2xl font-bold text-black-400">0</p>
              </div>
              <div className="bg-gray-200 p-4 rounded-xl hover:bg-red-200 shadow text-center flex flex-col justify-center items-center">
                <h3 className="text-gray-500 font-medium text-sm">Overdue</h3>
                <p className="text-2xl font-bold text-black-400">5</p>
              </div>
              <div className="bg-gray-200 p-4 rounded-xl hover:bg-red-300 shadow text-center flex flex-col justify-center items-center">
                <h3 className="text-gray-500 font-medium text-sm">Rejected</h3>
                <p className="text-2xl font-bold text-black-400">0</p>
              </div>
            </div>
          )}

          {selectedView === "Line Graph" && (
            <div className="w-full h-80">
              <ResponsiveContainer>
                <LineChart
                  data={[
                    { name: "Overdue", value: 30 },
                    { name: "WIP", value: 45 },
                    { name: "Pending With Reason", value: 28 },
                    { name: "Closed", value: 60 },
                    { name: "Rejected", value: 50 },
                  ]}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis
                    domain={[0, 100]}
                    ticks={[0, 25, 50, 75, 100]}
                    tickFormatter={(tick) => `${tick}%`}
                  />
                  <Tooltip formatter={(value) => `${value}%`} />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="value"
                    stroke="#3b82f6"
                    strokeWidth={3}
                    dot={{ r: 5, fill: "#3b82f6" }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          )}

          {selectedView === "Donut Graph" && (
            <div className="p-6">
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={data} // make sure `data` is defined in your component
                    dataKey="value"
                    nameKey="name"
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={100}
                    fill="#8884d8"
                    paddingAngle={5}
                    label
                  >
                    {data.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={COLORS[entry.name]} // assign color based on name
                      />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          )}
        </div>

        {/* Summary Table */}
        <div className="overflow-x-auto border rounded-lg shadow mt-6">
          <table className="min-w-full border-collapse text-sm text-gray-700 text-center">
            <thead>
              <tr>
                <th className="border w-1/6 px-4 py-3 bg-gray-200 font-semibold text-left">
                  Scheduled Type
                </th>
                <th className="border w-1/6 px-4 py-3 bg-red-300 font-semibold">
                  Overdue
                </th>
                <th className="border w-1/6 px-4 py-3 bg-blue-300 font-semibold">
                  Pending with Reason
                </th>
                <th className="border w-1/6 px-4 py-3 bg-yellow-300 font-semibold">
                  Work In Progress
                </th>
                <th className="border w-1/6 px-4 py-3 bg-green-300 font-semibold">
                  Closed
                </th>
                <th className="border w-1/6 px-4 py-3 bg-gray-200 font-semibold">
                  Total
                </th>
              </tr>
            </thead>
            <tbody>
              {tableData.map((row, idx) => (
                <tr key={idx} className="hover:bg-gray-50">
                  <td className="border px-4 py-2 text-left">{row.type}</td>
                  <td className="border px-4 py-2 bg-red-100">{row.overdue}</td>
                  <td className="border px-4 py-2 bg-blue-100">
                    {row.pending}
                  </td>
                  <td className="border px-4 py-2 bg-yellow-100">{row.wip}</td>
                  <td className="border px-4 py-2 bg-green-100">
                    {row.closed}
                  </td>
                  <td className="border px-4 py-2">{row.total}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Work In Progress Summary */}
        <div className="bg-white rounded-xl shadow p-6 mt-6">
          <h2 className="text-lg font-bold mb-4">Work In Progress Summary</h2>
          <div className="h-64 flex items-center justify-center text-gray-400">
            [Graph Placeholder]
          </div>
        </div>

        {/* 📊 Status Summary Chart */}
        <section className="w-full py-6">
          <div className="bg-white p-8 rounded-xl shadow-md">
            <h3 className="text-lg font-semibold mb-6">Status Summary</h3>
            <ResponsiveContainer width="100%" height={400}>
              <BarChart
                layout="vertical"
                data={summaryData}
                margin={{ top: 20, right: 30, left: 100, bottom: 20 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis
                  type="category"
                  dataKey="name"
                  width={250}
                  tick={{ fontSize: 12 }}
                />
                <Bar dataKey="count" fill="#06b6d4" radius={[4, 4, 4, 4]}>
                  <LabelList dataKey="count" position="right" />
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>
        </section>
        {/* 👨‍💻 Employee Efficiency Table */}
        <section className="w-full py-3">
          <div className="bg-white p-8 rounded-2xl shadow-lg">
            {/* Header with tabs */}
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold text-gray-800">
                Employee Efficiency
              </h3>
              <div className="flex gap-2 bg-gray-100 rounded-lg p-1">
                <button className="px-4 py-1.5 rounded-md bg-blue-600 text-white text-sm font-medium shadow-sm">
                  Assigned
                </button>
                <button className="px-4 py-1.5 rounded-md text-gray-600 hover:bg-gray-200 text-sm font-medium">
                  Raised
                </button>
                <button className="px-4 py-1.5 rounded-md text-gray-600 hover:bg-gray-200 text-sm font-medium">
                  CheckList
                </button>
              </div>
            </div>

            {/* Table */}
            <div className="overflow-x-auto">
              <table className="w-full border-collapse text-sm">
                <thead className="bg-gray-50 sticky top-0">
                  <tr className="text-gray-600">
                    <th className="p-3 text-left">Name</th>
                    <th className="p-3 text-left">Assigned</th>
                    <th className="p-3 text-left">Pending</th>
                    <th className="p-3 text-left">Rejected</th>
                    <th className="p-3 text-left">Completed</th>
                    <th className="p-3 text-left">Over Due</th>
                    <th className="p-3 text-left">Avg Time To Close</th>
                    <th className="p-3 text-left">Progress</th>
                  </tr>
                </thead>
                <tbody>
                  {efficiencyData.map((row, idx) => (
                    <tr
                      key={idx}
                      className="border-b hover:bg-gray-50 transition"
                    >
                      <td className="p-3 font-medium text-gray-700 ">
                        {row.name}
                      </td>
                      <td className="p-3 text-blue-600">{row.assigned}</td>
                      <td className="p-3">{row.pending}</td>
                      <td className="p-3">{row.rejected}</td>
                      <td className="p-3 text-blue-600">{row.completed}</td>
                      <td className="p-3">{row.overdue}</td>
                      <td className="p-3 text-gray-600">{row.time}</td>
                      <td className="p-3">
                        <div className="flex items-center gap-2">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full ${
                                row.progress >= 75
                                  ? "bg-teal-500"
                                  : row.progress >= 50
                                  ? "bg-sky-500"
                                  : row.progress >= 25
                                  ? "bg-orange-400"
                                  : "bg-red-500"
                              }`}
                              style={{ width: `${row.progress}%` }}
                            ></div>
                          </div>
                          <span className="text-xs font-medium text-gray-500 w-12 text-right">
                            {row.progress.toFixed(1)}%
                          </span>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Legend */}
            <div className="flex gap-6 items-center mt-6 text-xs text-gray-600">
              <span className="flex items-center gap-1">
                <span className="w-3 h-3 bg-red-500 rounded-full"></span> 1-24%
              </span>
              <span className="flex items-center gap-1">
                <span className="w-3 h-3 bg-orange-400 rounded-full"></span>{" "}
                25-49%
              </span>
              <span className="flex items-center gap-1">
                <span className="w-3 h-3 bg-sky-500 rounded-full"></span> 50-74%
              </span>
              <span className="flex items-center gap-1">
                <span className="w-3 h-3 bg-teal-500 rounded-full"></span>{" "}
                75-100%
              </span>
            </div>
          </div>
        </section>

        {/* Geographic Overview */}
        {/* <section className="w-full py-3">
          <div className="bg-white p-8 rounded-xl shadow-md">
            <h3 className="text-lg font-semibold mb-6">Geographic Overview</h3>
            <Plot
              data={geoData}
              layout={layout}
              style={{ width: "100%", height: "500px" }}
            />
          </div>
        </section> */}
        <div style={{ display: "flex" }}>
          <Plot
            data={geodata}
            layout={layout}
            useResizeHandler
            style={{ width: "80%", height: "100vh" }}
            config={{ responsive: true }}
          />

          {/* Side Table */}
          <div style={{ width: "20%", padding: "20px" }}>
            <table style={{ width: "100%", borderCollapse: "collapse" }}>
              <thead>
                <tr>
                  <th
                    style={{
                      textAlign: "left",
                      borderBottom: "1px solid #ccc",
                    }}
                  >
                    State
                  </th>
                  <th
                    style={{
                      textAlign: "right",
                      borderBottom: "1px solid #ccc",
                    }}
                  >
                    Closed
                  </th>
                </tr>
              </thead>
              <tbody>
                {stateData.map((row, index) => (
                  <tr key={index}>
                    <td style={{ padding: "8px 0" }}>{row.state}</td>
                    <td style={{ textAlign: "right" }}>{row.value}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Dashboard;
