{"name": "cryotas", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fullcalendar/daygrid": "^6.1.19", "@fullcalendar/interaction": "^6.1.19", "@fullcalendar/react": "^6.1.19", "@fullcalendar/timegrid": "^6.1.19", "@tailwindcss/vite": "^4.1.12", "lucide-react": "^0.541.0", "plotly.js": "^3.1.0", "react": "^19.1.1", "react-calendar": "^6.0.0", "react-dom": "^19.1.1", "react-icons": "^5.5.0", "react-plotly.js": "^2.6.0", "react-router-dom": "^7.8.1", "recharts": "^3.1.2", "tailwindcss": "^4.1.12"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.1.2"}}