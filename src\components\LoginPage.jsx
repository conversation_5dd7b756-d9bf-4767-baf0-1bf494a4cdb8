// import React from "react";
// import loginImage from "../assets/dci_logo.jpg"; // Use your uploaded image here

// const LoginPage = () => {

//   return (
//     <div className="min-h-screen flex">
//       {/* Left Image Section */}
//       <div className="hidden md:flex w-1/2 bg-gray-400 justify-center items-center">
//         <img
//           src={loginImage}
//           alt="Dashboard Preview"
//           className="max-w-1/2 max-h-1/2"
//         />
//       </div>

//       {/* Right Form Section */}
//       <div className="flex flex-1 justify-center items-center bg-white">
//         <div className="w-full max-w-md p-8">
//           <h1 className="text-3xl font-bold text-center text-gray-800 mb-6">
//             <span className="text-red-500"><PERSON><PERSON></span>
//           </h1>
//           <p className="text-center text-gray-600 mb-8">Login to Your Account!</p>

//           <form className="space-y-6">
//             <div>
//               <label htmlFor="email" className="block text-sm font-medium text-gray-700">
//                 Email <span className="text-red-500">*</span>
//               </label>
//               <input
//                 type="email"
//                 id="email"
//                 placeholder="Enter your email"
//                 className="mt-1 block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
//                 required
//               />
//             </div>

//             <div>
//               <label htmlFor="password" className="block text-sm font-medium text-gray-700">
//                 Password <span className="text-red-500">*</span>
//               </label>
//               <input
//                 type="password"
//                 id="password"
//                 placeholder="Enter your password"
//                 className="mt-1 block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
//                 required
//               />
//             </div>

//             <div className="flex items-center justify-between">
//               <label className="flex items-center text-sm text-gray-600">
//                 <input type="checkbox" className="mr-2" />
//                 Remember Me
//               </label>
//               <a href="#" className="text-blue-600 text-sm hover:underline">
//                 Forgot Password?
//               </a>
//             </div>

//             <button
//               type="submit"
//               className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
//             >
//               Login
//             </button>
//           </form>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default LoginPage;

// import React from "react";
// import loginImage from "../assets/dci_logo.jpg";
// import bgImage from "../assets/bg_logo.jpg"; // <-- add your uploaded image here

// const LoginPage = () => {
//   return (
//     <div className="min-h-screen flex">
//       {/* Left Image Section */}
//       <div className="hidden md:flex w-1/2 bg-gray-400 justify-center items-center">
//         <img
//           src={loginImage}
//           alt="Dashboard Preview"
//           className="max-w-1/2 max-h-1/2"
//         />
//       </div>

//       {/* Right Form Section with Background */}
//       <div
//         className="flex flex-1 justify-center items-center bg-cover bg-center"
//         style={{ backgroundImage: `url(${bgImage})` }}
//       >
//         <div className="w-full max-w-md p-8 bg-white bg-opacity-90 rounded-lg shadow-lg">
//           <h1 className="text-3xl font-bold text-center text-gray-800 mb-6">
//             <span className="text-red-500">Danieli Corus</span>
//           </h1>
//           <p className="text-center text-gray-600 mb-8">
//             Login to Your Account!
//           </p>

//           <form className="space-y-6">
//             <div>
//               <label
//                 htmlFor="email"
//                 className="block text-sm font-medium text-gray-700"
//               >
//                 Email <span className="text-red-500">*</span>
//               </label>
//               <input
//                 type="email"
//                 id="email"
//                 placeholder="Enter your email"
//                 className="mt-1 block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
//                 required
//               />
//             </div>

//             <div>
//               <label
//                 htmlFor="password"
//                 className="block text-sm font-medium text-gray-700"
//               >
//                 Password <span className="text-red-500">*</span>
//               </label>
//               <input
//                 type="password"
//                 id="password"
//                 placeholder="Enter your password"
//                 className="mt-1 block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
//                 required
//               />
//             </div>

//             <div className="flex items-center justify-between">
//               <label className="flex items-center text-sm text-gray-600">
//                 <input type="checkbox" className="mr-2" />
//                 Remember Me
//               </label>
//               <a href="#" className="text-blue-600 text-sm hover:underline">
//                 Forgot Password?
//               </a>
//             </div>

//             <button
//               type="submit"
//               className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
//             >
//               Login
//             </button>
//           </form>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default LoginPage;






import React from "react";
import loginImage from "../assets/dci_logo.jpg";
import bgImage from "../assets/bg_logo.jpg"; 
import logo2 from "../assets/logo2.jpg";

const LoginPage = () => {
  return (
    <div className="min-h-screen flex">
      {/* Left Image Section */}
      <div
        className="flex flex-1 justify-center items-center bg-cover bg-center "
        style={{ backgroundImage: `url(${bgImage})` }}
      >
        <img
          src={logo2}
          alt="Dashboard Preview"
          className="max-w- max-h-1/2"
        />
      </div>

      {/* Right Form Section with Background */}
      <div className="hidden md:flex w-1/2 bg-gray-400 justify-center items-center">
        <div className="w-full max-w-md p-8 bg-gray-100 bg-opacity-90 rounded-lg shadow-lg">
          <h1 className="text-3xl font-bold text-center text-gray-800 mb-6">
            <span className="text-3xl font-bold bg-gradient-to-r from-orange-700 to-blue-700 bg-clip-text text-transparent">Danieli Corus</span>
          </h1>
          <p className="text-center text-gray-600 mb-8">
            Login to Your Account!
          </p>

          <form className="space-y-6">
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700"
              >
                Email <span className="text-red-500">*</span>
              </label>
              <input
                type="email"
                id="email"
                placeholder="Enter your email"
                className="mt-1 block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>

            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700"
              >
                Password <span className="text-red-500">*</span>
              </label>
              <input
                type="password"
                id="password"
                placeholder="Enter your password"
                className="mt-1 block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>

            <div className="flex items-center justify-between">
              <label className="flex items-center text-sm text-gray-600">
                <input type="checkbox" className="mr-2" />
                Remember Me
              </label>
              <a href="#" className="text-blue-600 text-sm hover:underline">
                Forgot Password?
              </a>
            </div>

            <button
              type="submit"
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
            >
              Login
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
