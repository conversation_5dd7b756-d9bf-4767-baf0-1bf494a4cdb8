import React, { useState } from "react";

const list = [
  {
    id: "#60230010",
    name: "NITROGEN HOSE N2 DN15 ASSEMBLY ((L-8500 mm)) - ANUGUL",
    quantity: 2,
    status: "Active",
    date: "26/07/2025",
  },
  {
    id: "#60230015",
    name: "NITROGEN HOSE N2 DN15 ASSEMBLY (L-15500 mm) - ANUGUL",
    quantity: 1,
    status: "Active",
    date: "26/07/2025",
  },
  {
    id: "#60230008",
    name: "COOLING WATER HOSE DN80 SLIP-ON FLANGE RETURN (L-16000 mm) - ANUGUL",
    quantity: 1,
    status: "Active",
    date: "26/07/2025",
  },
  {
    id: "#60230009",
    name: "COOLING WATER HOSE DN80 SLIP-ON FLANGE SUPPLY (L-16000 mm) - ANUGUL",
    quantity: 1,
    status: "Active",
    date: "26/07/2025",
  },
  {
    id: "#60230013",
    name: "COOLING WATER HOSE DN80 SLIP-ON FLANGE RETURN (L-8570 mm) - ANUGUL",
    quantity: 1,
    status: "Active",
    date: "26/07/2025",
  },
  {
    id: "#60230014",
    name: "COOLING WATER HOSE DN80 SLIP-ON FLANGE SUPPLY (L-8570 mm) - ANUGUL",
    quantity: 1,
    status: "Active",
    date: "26/07/2025",
  },
];

const NeedStockList = [
  {
    id: "#60230010",
    name: "NITROGEN HOSE N2 DN15 ASSEMBLY ((L-8500 mm)) - ANUGUL",
    quantity: 2,
    status: "Active",
    date: "26/07/2025",
  },
];

const EquipmentManagement = () => {
  const [activeList, setActiveList] = useState("list"); // default: list
  const [showPartModal, setShowPartModal] = useState(false);

  // choose which list to show
  const displayedData = activeList === "list" ? list : NeedStockList;

  return (
    <div>
      {/* Navbar */}
      <nav className="bg-gray-100 p-4 shadow-md fixed top-[-14px] left-64 right-0 z-50">
        {/* Row 1 - Heading */}
        <div className="flex justify-between items-center mb-3">
          <h1 className="text-lg font-bold">Parts</h1>
        </div>

        {/* Row 2 - Buttons + Search Bar */}
        <div className="flex items-center">
          {/* Left buttons */}
          <button
            onClick={() => setActiveList("list")}
            className={`w-20 border rounded-lg px-4 py-2 bg-white hover:bg-gray-100 focus:outline-none ${
              activeList === "list"
                ? "ring-2 ring-blue-200 border-blue-400"
                : "border-gray-300"
            }`}
          >
            List
          </button>
          <button
            onClick={() => setActiveList("needStock")}
            className={`w-40 border rounded-lg px-4 py-2 bg-white hover:bg-gray-100 focus:outline-none ${
              activeList === "needStock"
                ? "ring-2 ring-blue-200 border-blue-400"
                : "border-gray-300"
            }`}
          >
            Need Stock List
          </button>

          {/* Search bar */}
          <div className="relative ml-4">
            <input
              type="text"
              placeholder="Search by Name/Serial No/QR Code/Part No"
              className="w-80 border border-gray-300 rounded-lg px-4 py-2 pl-10 focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-blue-400"
            />
            <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
              <i className="fa fa-search"></i>
            </span>
          </div>

          {/* Right-side conditional button */}
          <div className="flex-1 flex justify-end">
            {activeList === "list" ? (
              <button
                onClick={() => setShowPartModal(true)} // ✅ Open modal
                className="flex items-center space-x-2 bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-700"
              >
                <span className="text-lg">
                  <i className="fas fa-plus-circle pr-1"></i>
                </span>
                <span>Part</span>
              </button>
            ) : (
              <button className="flex items-center space-x-2 bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                <span className="text-lg">
                  <i className="fas fa-plus-circle pr-1"></i>
                </span>
                <span>Create PR</span>
              </button>
            )}
          </div>
        </div>
      </nav>

      {/* === Popup Modal for Part Form === */}
      {showPartModal && (
        <div className="fixed inset-0 flex items-center justify-center bg-gray bg-opacity-50 z-[999]">
          <div className="bg-white w-11/12 max-w-5xl rounded-lg shadow-lg overflow-y-auto max-h-[90vh] p-6">
            {/* Header */}
            <div className="flex justify-between items-center border-none pb-3">
              <h2 className="text-xl font-bold">Part Details</h2>
              <button
                onClick={() => setShowPartModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                ✖
              </button>
            </div>

            {/* Body Form */}
            <div className="grid grid-cols-4 gap-4 mt-4">
              <input type="text" placeholder="Name *" className="border p-2 rounded" />
              <input type="text" placeholder="Serial Number *" className="border p-2 rounded" />
              <input type="number" placeholder="Minimum Qty *" className="border p-2 rounded" />
              <input type="number" placeholder="Maximum Qty" className="border p-2 rounded" />
              <input type="text" placeholder="Part Number" className="border p-2 rounded" />
              <input type="text" placeholder="Specification" className="border p-2 rounded" />
              <input type="text" placeholder="Make" className="border p-2 rounded" />
              <input type="text" placeholder="Manufacturer" className="border p-2 rounded" />
              <input type="text" placeholder="Department" className="border p-2 rounded" />
              <input type="text" placeholder="Spare Group" className="border p-2 rounded" />
              <input type="text" placeholder="Cost Method" className="border p-2 rounded" />
              <input type="text" placeholder="End of life date" className="border p-2 rounded" />

              {/* Image Upload */}
              <div className="col-span-2">
                <label className="block text-sm font-medium mb-1">Upload Image</label>
                <input type="file" className="border p-2 rounded " />              {/* w-full */}
              </div>
            </div>

            {/* Footer Buttons */}
            <div className="flex justify-end space-x-3 mt-6 border-none pt-3">
              <button
                onClick={() => setShowPartModal(false)}
                className="px-4 py-2 border rounded-md hover:bg-gray-100"
              >
                Cancel
              </button>
              <button className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-700">
                Save
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Data List */}
      <div className="pt-20 p-4 space-y-4">
        {displayedData.map((part, index) => (
          <div
            key={index}
            className="flex items-center justify-between bg-white shadow rounded-lg p-4 border"
          >
            {/* Left image placeholder */}
            <div className="w-16 h-16 bg-gray-200 rounded flex items-center justify-center">
              <span className="text-gray-400 text-sm">Img</span>
            </div>

            {/* Middle content */}
            <div className="flex-1 px-4">
              <p className="font-semibold">{part.id}</p>
              <p className="text-gray-700 text-sm">{part.name}</p>
              <div className="flex items-center mt-2 text-sm text-gray-600">
                <span className="mr-2">📄 {part.quantity}</span>
              </div>
            </div>

            {/* Right side status & date */}
            <div className="text-right">
              <span
                className={`px-3 py-1 text-sm font-medium rounded-full ${
                  part.status === "Active"
                    ? "bg-green-100 text-green-600"
                    : "bg-red-100 text-red-600"
                }`}
              >
                {part.status}
              </span>
              <p className="text-xs text-gray-500 mt-1">{part.date}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default EquipmentManagement;
