// import React from "react";
// import { Outlet, <PERSON> } from "react-router-dom";
// import { <PERSON>a<PERSON><PERSON><PERSON><PERSON>, Fa<PERSON><PERSON><PERSON>List, FaUserCog } from "react-icons/fa";
// import logo from "../assets/dci_logo.jpg";

// const Layout = () => {
//   return (
//     <div className="flex h-screen bg-gray-100">
//       {/* Sidebar */}
//       <aside className="w-64 bg-white shadow-md flex flex-col">
//         <div className="flex items-center justify-center h-16 border-b">
//           <img src={logo} alt="Logo" className="h-10" />
//         </div>
//         <nav className="flex-1 p-4 space-y-2">
//           <Link
//             to="/"
//             className="flex items-center p-2 rounded-lg hover:bg-blue-100"
//           >
//             <FaChartPie className="mr-3 text-blue-600" /> Dashboard
//           </Link>
//           <Link
//             to="/inventory-management"
//             className="flex items-center p-2 rounded-lg hover:bg-blue-100"
//           >
//             <FaClipboardList className="mr-3 text-blue-600" /> InventoryManagement
//           </Link>
//           <Link
//             to="/login"
//             className="flex items-center p-2 rounded-lg hover:bg-blue-100"
//           >
//             <FaUserCog className="mr-3 text-blue-600" /> Login
//           </Link>
//         </nav>
//       </aside>

//       {/* Page Content */}
//       <main className="flex-1 p-6 overflow-y-auto">
//         <Outlet />
//       </main>
//     </div>
//   );
// };

// export default Layout;




// import React, { useState } from "react";
// import { Outlet, Link } from "react-router-dom";
// import { FaChartPie, FaClipboardList, FaUserCog } from "react-icons/fa";
// import { ChevronDown, ChevronRight } from "lucide-react";
// import logo from "../assets/dci_logo.jpg";

// const Layout = () => {
//   const [openMenu, setOpenMenu] = useState(false);

//   return (
//     <div className="flex h-screen bg-gray-100">
//       {/* Sidebar */}
//       <aside className="w-64 bg-white shadow-md flex flex-col">
//         <div className="flex items-center justify-center h-16 border-b">
//           <img src={logo} alt="Logo" className="h-10" />
//         </div>
//         <nav className="flex-1 p-4 space-y-2">
//           {/* Dashboard */}
//           <Link
//             to="/"
//             className="flex items-center p-2 rounded-lg hover:bg-blue-100"
//           >
//             <FaChartPie className="mr-3 text-blue-600" /> Dashboard
//           </Link>

//           {/* Inventory Management (Expandable)
//           <div>
//             <button
//               onClick={() => setOpenMenu(!openMenu)}
//               className="flex items-center justify-between w-full p-2 rounded-lg hover:bg-blue-100"
//             >
//               <span className="flex items-center">
//                 <FaClipboardList className="mr-3 text-blue-600" />
//                 InventoryManagement
//               </span>
//               {openMenu ? <ChevronDown size={18} /> : <ChevronRight size={18} />}
//             </button>

//             {/* Submenu */}
//           {/*{openMenu && (
//               <div className="ml-8 mt-2 space-y-2 text-sm">
//                 <Link
//                   to="/inventory-management/assets"
//                   className="block p-2 rounded-md hover:bg-blue-50"
//                 >
//                   Assets
//                 </Link>
//                 <Link
//                   to="/inventory-management/inventory"
//                   className="block p-2 rounded-md hover:bg-blue-50"
//                 >
//                   Inventory
//                 </Link>
//               </div>
//             )}
//           </div> */}

//           {/* Inventory Management (Expandable) */}
//           <div>
//             <button
//               onClick={() => setOpenMenu(!openMenu)}
//               className="flex items-center justify-between w-full p-2 rounded-lg hover:bg-blue-100"
//             >
//               <span className="flex items-center">
//                 <FaClipboardList className="mr-3 text-blue-600" />
//                 InventoryManagement
//               </span>
//               {openMenu ? (
//                 <ChevronDown size={18} />
//               ) : (
//                 <ChevronRight size={18} />
//               )}
//             </button>

//             {/* Submenu */}
//             {openMenu && (
//               <div className="ml-6 mt-2 space-y-2 text-sm">
//                 {/* Assets (Expandable) */}
//                 <div>
//                   <button
//                     onClick={() => setOpenAssets(!openAssets)}
//                     className="flex items-center justify-between w-full p-2 rounded-md hover:bg-blue-50"
//                   >
//                     <span>Assets</span>
//                     {openAssets ? (
//                       <ChevronDown size={16} />
//                     ) : (
//                       <ChevronRight size={16} />
//                     )}
//                   </button>
//                   {openAssets && (
//                     <div className="ml-6 mt-1 space-y-1">
//                       <Link
//                         to="/inventory-management/assets/facility"
//                         className="block p-2 rounded-md hover:bg-blue-50"
//                       >
//                         Facility Management
//                       </Link>
//                       <Link
//                         to="/inventory-management/assets/equipment"
//                         className="block p-2 rounded-md hover:bg-blue-50"
//                       >
//                         Equipment Management
//                       </Link>
//                     </div>
//                   )}
//                 </div>

//                 {/* Inventory (Expandable) */}
//                 <div>
//                   <button
//                     onClick={() => setOpenInventory(!openInventory)}
//                     className="flex items-center justify-between w-full p-2 rounded-md hover:bg-blue-50"
//                   >
//                     <span>Inventory</span>
//                     {openInventory ? (
//                       <ChevronDown size={16} />
//                     ) : (
//                       <ChevronRight size={16} />
//                     )}
//                   </button>
//                   {openInventory && (
//                     <div className="ml-6 mt-1 space-y-1">
//                       <Link
//                         to="/inventory-management/inventory/create"
//                         className="block p-2 rounded-md hover:bg-blue-50"
//                       >
//                         Create Inventory
//                       </Link>
//                       <Link
//                         to="/inventory-management/inventory/update"
//                         className="block p-2 rounded-md hover:bg-blue-50"
//                       >
//                         Update Inventory
//                       </Link>
//                       <Link
//                         to="/inventory-management/inventory/logs"
//                         className="block p-2 rounded-md hover:bg-blue-50"
//                       >
//                         Activity Logs
//                       </Link>
//                     </div>
//                   )}
//                 </div>
//               </div>
//             )}
//           </div>

//           {/* Login */}
//           <Link
//             to="/login"
//             className="flex items-center p-2 rounded-lg hover:bg-blue-100"
//           >
//             <FaUserCog className="mr-3 text-blue-600" /> Login
//           </Link>
//         </nav>
//       </aside>

//       {/* Page Content */}
//       <main className="flex-1 p-6 overflow-y-auto">
//         <Outlet />
//       </main>
//     </div>
//   );
// };

// export default Layout;




import React, { useState } from "react";
import { Outlet, Link } from "react-router-dom";
import { FaChartPie, FaClipboardList, FaUserCog } from "react-icons/fa";
import { ChevronDown, ChevronRight } from "lucide-react";
import logo from "../assets/dci_logo.jpg";

const Layout = () => {
  const [openMenu, setOpenMenu] = useState(false);
  const [openAssets, setOpenAssets] = useState(false);
  const [openInventory, setOpenInventory] = useState(false);

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <aside className="w-64 bg-white shadow-md flex flex-col">
        <div className="flex items-center justify-center h-16 border-b">
          <img src={logo} alt="Logo" className="h-10" />
        </div>
        <nav className="flex-1 p-4 space-y-2">
          {/* Dashboard */}
          <Link
            to="/"
            className="flex items-center p-2 rounded-lg hover:bg-blue-100"
          >
            <FaChartPie className="mr-3 text-blue-600" /> Dashboard
          </Link>

          {/* Inventory Management (Expandable) */}
          <div>
            <button
              onClick={() => setOpenMenu(!openMenu)}
              className="flex items-center justify-between w-full p-2 rounded-lg hover:bg-blue-100"
            >
              <span className="flex items-center">
                <FaClipboardList className="mr-3 text-blue-600" />
                InventoryManagement
              </span>
              {openMenu ? (
                <ChevronDown size={18} />
              ) : (
                <ChevronRight size={18} />
              )}
            </button>

            {/* Submenu */}
            {openMenu && (
              <div className="ml-6 mt-2 space-y-2 text-sm">
                {/* Assets (Expandable) */}
                <div>
                  <button
                    onClick={() => setOpenAssets(!openAssets)}
                    className="flex items-center justify-between w-full p-2 rounded-md hover:bg-blue-50"
                  >
                    <span>Assets</span>
                    {openAssets ? (
                      <ChevronDown size={16} />
                    ) : (
                      <ChevronRight size={16} />
                    )}
                  </button>
                  {openAssets && (
                    <div className="ml-6 mt-1 space-y-1">
                      <Link
                        to="/inventory-management/assets/facility"
                        className="block p-2 rounded-md hover:bg-blue-50"
                      >
                        Facility Management
                      </Link>
                      <Link
                        to="/inventory-management/assets/equipment"
                        className="block p-2 rounded-md hover:bg-blue-50"
                      >
                        Equipment Management
                      </Link>
                    </div>
                  )}
                </div>

                {/* Inventory (Expandable) */}
                <div>
                  <button
                    onClick={() => setOpenInventory(!openInventory)}
                    className="flex items-center justify-between w-full p-2 rounded-md hover:bg-blue-50"
                  >
                    <span>Inventory</span>
                    {openInventory ? (
                      <ChevronDown size={16} />
                    ) : (
                      <ChevronRight size={16} />
                    )}
                  </button>
                  {openInventory && (
                    <div className="ml-6 mt-1 space-y-1">
                      <Link
                        to="/inventory-management/inventory/create"
                        className="block p-2 rounded-md hover:bg-blue-50"
                      >
                        Create Inventory
                      </Link>
                      <Link
                        to="/inventory-management/inventory/update"
                        className="block p-2 rounded-md hover:bg-blue-50"
                      >
                        Update Inventory
                      </Link>
                      
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

            {/* Workflow Automation (Expandable) */}
          <div>
            <button
                    onClick={() => setOpenAssets(!openAssets)}
                    className="flex items-center justify-between w-full p-2 rounded-md hover:bg-blue-50"
                  >
                    <span>Workflow Automation</span>
                    {openAssets ? (
                      <ChevronDown size={16} />
                    ) : (
                      <ChevronRight size={16} />
                    )}
                  </button>
                  {openAssets && (
                    <div className="ml-6 mt-1 space-y-1">
                      <Link
                        to="/inventory-management/assets/facility"
                        className="block p-2 rounded-md hover:bg-blue-50"
                      >
                        Workflows
                      </Link>
                      
                    </div>
                  )}
          </div>

          {/* Login */}
          <Link
            to="/login"
            className="flex items-center p-2 rounded-lg hover:bg-blue-100"
          >
            <FaUserCog className="mr-3 text-blue-600" /> Login
          </Link>
        </nav>
      </aside>

      {/* Page Content */}
      <main className="flex-1 p-6 overflow-y-auto">
        <Outlet />
      </main>
    </div>
  );
};

export default Layout;
