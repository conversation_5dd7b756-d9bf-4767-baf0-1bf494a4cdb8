// import React from "react";

// const FacilityManagement = () => {
//   return (
//     <div className="p-4">
//       <h3 className="text-lg font-bold">Facility Management</h3>
      
//     </div>
//   );
// };

// export default FacilityManagement;



import React, { useState } from "react";

const initialAssets = [
  {
    assetName: "SUBLANCE B JSW DOLVI",
    serialNo: "#JSWD-SUB-B",
    accountName: "J S W",
    location: "DOLVI",
    category: "Equipment",
    creationDate: "22/05/2024 15:49",
    updateDate: "26/08/2025 10:04",
    status: "Active",
  },
  {
    assetName: "SUBLANCE D JSW TORNAGALLU",
    serialNo: "#JSWT-SUB-D",
    accountName: "J S W",
    location: "TORNAGALLU",
    category: "Equipment",
    creationDate: "22/05/2024 15:49",
    updateDate: "19/08/2025 10:22",
    status: "Active",
  },
  {
    assetName: "SUBLANCE A JSW DOLVI",
    serialNo: "#JSWD-SUB-A",
    accountName: "J S W",
    location: "DOLVI",
    category: "Equipment",
    creationDate: "22/05/2024 15:49",
    updateDate: "18/08/2025 14:51",
    status: "Active",
  },
  {
    assetName: "PLC",
    serialNo: "#JSWT-SUB-D-PLC",
    accountName: "J S W",
    location: "TORNAGALLU",
    category: "Equipment",
    creationDate: "29/04/2024 11:48",
    updateDate: "15/07/2025 10:10",
    status: "Active",
  },
];

const FacilityManagement = () => {
  const [assets, setAssets] = useState(initialAssets);
  const [search, setSearch] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [newAsset, setNewAsset] = useState({
    assetName: "",
    serialNo: "",
    accountName: "",
    location: "",
    category: "",
    creationDate: new Date().toLocaleString(),
    updateDate: new Date().toLocaleString(),
    status: "Active",
  });

  const filteredAssets = assets.filter(
    (a) =>
      a.assetName.toLowerCase().includes(search.toLowerCase()) ||
      a.serialNo.toLowerCase().includes(search.toLowerCase())
  );

  const handleAddAsset = () => {
    if (!newAsset.assetName || !newAsset.serialNo) return;

    setAssets([...assets, { ...newAsset }]);
    setIsModalOpen(false);

    // Reset form
    setNewAsset({
      assetName: "",
      serialNo: "",
      accountName: "",
      location: "",
      category: "",
      creationDate: new Date().toLocaleString(),
      updateDate: new Date().toLocaleString(),
      status: "Active",
    });
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold text-gray-800">
          Facility Management <span className="ml-2 text-sm text-blue-600">{assets.length}</span>
        </h2>
        <div className="flex items-center gap-3">
          <button
            onClick={() => setIsModalOpen(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 shadow"
          >
            + Assets
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-wrap gap-3 mb-6">
        <input
          type="text"
          placeholder="Asset Name / Serial No / Machine Code / Part No"
          className="flex-1 px-4 py-2 border rounded-md"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
        />
        <select className="px-3 py-2 border rounded-md">
          <option>Active</option>
          <option>Inactive</option>
        </select>
        {/* <select className="px-3 py-2 border rounded-md">
          <option>Location</option>
        </select>
        <select className="px-3 py-2 border rounded-md">
          <option>Asset Type</option>
        </select>
        <select className="px-3 py-2 border rounded-md">
          <option>ABC Type</option>
        </select>
        <select className="px-3 py-2 border rounded-md">
          <option>Condition</option>
        </select>
        <select className="px-3 py-2 border rounded-md">
          <option>Warranty Status</option>
        </select> */}
      </div>

      {/* Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        {/* Scrollable table wrapper */}
        <div className="max-h-[300px] overflow-y-auto">
          <table className="min-w-full border-collapse">
            <thead className="bg-gray-100 text-gray-700 text-sm sticky top-0">
              <tr>
                <th className="p-3 text-left">Asset Name (Serial No)</th>
                <th className="p-3 text-left">Account Name</th>
                <th className="p-3 text-left">Location Name</th>
                <th className="p-3 text-left">Category Name</th>
                <th className="p-3 text-left">Creation Date</th>
                <th className="p-3 text-left">Updation Date</th>
                <th className="p-3 text-left">Status</th>
              </tr>
            </thead>
            <tbody>
              {filteredAssets.map((asset, idx) => (
                <tr key={idx} className="border-t hover:bg-gray-50">
                  <td className="p-3">
                    <div className="font-semibold text-gray-800">
                      {asset.assetName}
                    </div>
                    <div className="text-sm text-gray-500">
                      {asset.serialNo}
                    </div>
                  </td>
                  <td className="p-3">{asset.accountName}</td>
                  <td className="p-3">{asset.location}</td>
                  <td className="p-3">{asset.category}</td>
                  <td className="p-3">{asset.creationDate}</td>
                  <td className="p-3">{asset.updateDate}</td>
                  <td className="p-3">
                    <span className="px-2 py-1 rounded-full bg-green-100 text-green-600 text-sm font-medium">
                      ● {asset.status}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg shadow-lg p-6 w-96">
            <h2 className="text-lg font-semibold mb-4">Add New Asset</h2>

            <input
              type="text"
              placeholder="Asset Name"
              className="w-full p-2 mb-3 border rounded"
              value={newAsset.assetName}
              onChange={(e) =>
                setNewAsset({ ...newAsset, assetName: e.target.value })
              }
            />
            <input
              type="text"
              placeholder="Serial No"
              className="w-full p-2 mb-3 border rounded"
              value={newAsset.serialNo}
              onChange={(e) =>
                setNewAsset({ ...newAsset, serialNo: e.target.value })
              }
            />
            <input
              type="text"
              placeholder="Account Name"
              className="w-full p-2 mb-3 border rounded"
              value={newAsset.accountName}
              onChange={(e) =>
                setNewAsset({ ...newAsset, accountName: e.target.value })
              }
            />
            <input
              type="text"
              placeholder="Location"
              className="w-full p-2 mb-3 border rounded"
              value={newAsset.location}
              onChange={(e) =>
                setNewAsset({ ...newAsset, location: e.target.value })
              }
            />
            <input
              type="text"
              placeholder="Category"
              className="w-full p-2 mb-3 border rounded"
              value={newAsset.category}
              onChange={(e) =>
                setNewAsset({ ...newAsset, category: e.target.value })
              }
            />
            <select
              className="w-full p-2 mb-3 border rounded"
              value={newAsset.status}
              onChange={(e) =>
                setNewAsset({ ...newAsset, status: e.target.value })
              }
            >
              <option>Active</option>
              <option>Inactive</option>
            </select>

            <div className="flex justify-end gap-3">
              <button
                onClick={() => setIsModalOpen(false)}
                className="px-4 py-2 bg-gray-200 rounded"
              >
                Cancel
              </button>
              <button
                onClick={handleAddAsset}
                className="px-4 py-2 bg-blue-600 text-white rounded"
              >
                Add Asset
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FacilityManagement;
