// import React from "react";

// const CreateInventory = () => {
//   return (
//     <div className="p-4">
//         <h1>Create Inventory</h1>

//     </div>
//   );
// };

// export default CreateInventory;

// import React, { useState } from "react";
// import { User } from "lucide-react";

// const inventoryData = [
//   {
//     partName: "TSC PROBE-TORNAGALLU",
//     date: "29/08/2025 10:47",
//     voucher: "OUTWARD",
//     updatedBy: "ASHUTOSH",
//     received: { qty: 0, rate: 0, gross: 0 },
//     issued: { qty: 47, rate: 1483.17, gross: 69709.2 },
//     available: 2011,
//   },
//   {
//     partName: "Contact Block",
//     date: "29/08/2025 02:28",
//     voucher: "OUTWARD",
//     updatedBy: "Atanu Narayan",
//     received: { qty: 0, rate: 0, gross: 0 },
//     issued: { qty: 2, rate: 887.56, gross: 1775.12 },
//     available: 33,
//   },
//   {
//     partName: "TSOP PROBE-ANUGUL",
//     date: "29/08/2025 02:26",
//     voucher: "OUTWARD",
//     updatedBy: "Atanu Narayan",
//     received: { qty: 0, rate: 0, gross: 0 },
//     issued: { qty: 261, rate: 2131.83, gross: 556408.61 },
//     available: 2235,
//   },
//   {
//     partName: "TSC PROBE-ANUGUL",
//     date: "29/08/2025 02:25",
//     voucher: "OUTWARD",
//     updatedBy: "Atanu Narayan",
//     received: { qty: 0, rate: 0, gross: 0 },
//     issued: { qty: 262, rate: 1823.93, gross: 477868.91 },
//     available: 1491,
//   },
// ];

// const EquipmentManagement = () => {
//   const [activeTab, setActiveTab] = useState("list");

//   return (
//     <div>
//       {/* Header */}
//       <nav className="bg-white p-4 shadow-sm fixed top-0 left-64 right-0 z-50">
//         <h1 className="text-xl font-bold mb-4">Stock ledger</h1>

//         {/* Tab Navigation */}
//         <div className="flex items-center space-x-0 mb-4">
//           <button
//             className={`px-6 py-2 border font-medium `}
//           >
//             List
//           </button>
//         </div>

//       </nav>

//       {/* Table */}
//       <div className="pt-32 p-4">
//         <div className="bg-white shadow-lg rounded-lg border overflow-hidden">
//           <div className="overflow-x-auto">
//             {/*  */}
//             {/* Table */}
// <div className="pt-32 p-4">
//   <div className="bg-white shadow-lg rounded-lg border overflow-hidden w-full">
//     <div className="overflow-x-auto w-full">
//       <table className="w-full text-sm border-collapse table-fixed">
//         {/* Header */}
//         <thead className="bg-gray-100 text-gray-700">
//           <tr>
//             <th className="px-6 py-3 text-left border-r w-1/6">Part Name</th>
//             <th className="px-6 py-3 text-left border-r w-1/8">Date</th>
//             <th className="px-6 py-3 text-left border-r w-1/12">Voucher</th>
//             <th className="px-6 py-3 text-left border-r w-1/6">Stock Updated By</th>
//             <th className="px-6 py-3 text-center border-r w-1/4" colSpan="3">Received</th>
//             <th className="px-6 py-3 text-center border-r w-1/4" colSpan="3">Issued</th>
//             <th className="px-6 py-3 text-center w-1/12">Available Quantity</th>
//           </tr>
//           <tr className="bg-gray-50">
//             <th className="border-r"></th>
//             <th className="border-r"></th>
//             <th className="border-r"></th>
//             <th className="border-r"></th>
//             <th className="px-4 py-2 text-center border-r text-xs">Qty</th>
//             <th className="px-4 py-2 text-center border-r text-xs">Rate</th>
//             <th className="px-4 py-2 text-center border-r text-xs">Gross</th>
//             <th className="px-4 py-2 text-center border-r text-xs">Qty</th>
//             <th className="px-4 py-2 text-center border-r text-xs">Rate</th>
//             <th className="px-4 py-2 text-center border-r text-xs">Gross</th>
//             <th></th>
//           </tr>
//         </thead>

//         {/* Body */}
//         <tbody>
//           {inventoryData.map((row, index) => (
//             <tr key={index} className={`${index % 2 === 0 ? "bg-white" : "bg-gray-50"} hover:bg-blue-50`}>
//               <td className="px-6 py-3 border-r font-medium">{row.partName}</td>
//               <td className="px-6 py-3 border-r text-gray-600">{row.date}</td>
//               <td className="px-6 py-3 border-r">{row.voucher}</td>
//               <td className="px-6 py-3 border-r">
//                 <div className="flex items-center gap-2">
//                   <User className="w-4 h-4 text-gray-600" />
//                   <span>{row.updatedBy}</span>
//                 </div>
//               </td>

//               {/* Received columns */}
//               <td className="px-6 py-3 text-center border-r">{row.received.qty}</td>
//               <td className="px-6 py-3 text-center border-r">{row.received.rate}</td>
//               <td className="px-6 py-3 text-center border-r">{row.received.gross}</td>

//               {/* Issued columns */}
//               <td className="px-6 py-3 text-center border-r">{row.issued.qty}</td>
//               <td className="px-6 py-3 text-center border-r">{row.issued.rate}</td>
//               <td className="px-6 py-3 text-center border-r">{row.issued.gross}</td>

//               {/* Available quantity */}
//               <td className="px-6 py-3 text-center font-semibold">{row.available}</td>
//             </tr>
//           ))}
//         </tbody>
//       </table>
//     </div>
//   </div>
// </div>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default EquipmentManagement;

import React, { useState } from "react";
import { User } from "lucide-react";

const inventoryData = [
  {
    partName: "TSC PROBE-TORNAGALLU",
    date: "29/08/2025 10:47",
    voucher: "OUTWARD",
    updatedBy: "ASHUTOSH",
    received: { qty: 0, rate: 0, gross: 0 },
    issued: { qty: 47, rate: 1483.17, gross: 69709.2 },
    available: 2011,
  },
  {
    partName: "Contact Block",
    date: "29/08/2025 02:28",
    voucher: "OUTWARD",
    updatedBy: "Atanu Narayan",
    received: { qty: 0, rate: 0, gross: 0 },
    issued: { qty: 2, rate: 887.56, gross: 1775.12 },
    available: 33,
  },
  {
    partName: "TSOP PROBE-ANUGUL",
    date: "29/08/2025 02:26",
    voucher: "OUTWARD",
    updatedBy: "Atanu Narayan",
    received: { qty: 0, rate: 0, gross: 0 },
    issued: { qty: 261, rate: 2131.83, gross: 556408.61 },
    available: 2235,
  },
  {
    partName: "TSC PROBE-ANUGUL",
    date: "29/08/2025 02:25",
    voucher: "OUTWARD",
    updatedBy: "Atanu Narayan",
    received: { qty: 0, rate: 0, gross: 0 },
    issued: { qty: 262, rate: 1823.93, gross: 477868.91 },
    available: 1491,
  },
  {
    partName: "TSC PROBE-ANUGUL",
    date: "29/08/2025 02:25",
    voucher: "OUTWARD",
    updatedBy: "Atanu Narayan",
    received: { qty: 0, rate: 0, gross: 0 },
    issued: { qty: 262, rate: 1823.93, gross: 477868.91 },
    available: 1491,
  },
  {
    partName: "TSC PROBE-ANUGUL",
    date: "29/08/2025 02:25",
    voucher: "OUTWARD",
    updatedBy: "Atanu Narayan",
    received: { qty: 0, rate: 0, gross: 0 },
    issued: { qty: 262, rate: 1823.93, gross: 477868.91 },
    available: 1491,
  },
  {
    partName: "TSC PROBE-ANUGUL",
    date: "29/08/2025 02:25",
    voucher: "OUTWARD",
    updatedBy: "Atanu Narayan",
    received: { qty: 0, rate: 0, gross: 0 },
    issued: { qty: 262, rate: 1823.93, gross: 477868.91 },
    available: 1491,
  },
  {
    partName: "TSC PROBE-ANUGUL",
    date: "29/08/2025 02:25",
    voucher: "OUTWARD",
    updatedBy: "Atanu Narayan",
    received: { qty: 0, rate: 0, gross: 0 },
    issued: { qty: 262, rate: 1823.93, gross: 477868.91 },
    available: 1491,
  },
];

const EquipmentManagement = () => {
  const [activeTab, setActiveTab] = useState("list");

  return (
    <div>
      {/* Header */}
      <nav className="bg-white p-4 shadow-sm fixed top-0 left-64 right-0 z-50">
        <h1 className="text-xl font-bold mb-4">Stock ledger</h1>

        {/* Button */}
        <div className="flex justify-end">
        <button className="flex items-center space-x-1  bg-blue-400 text-white px-2 py-2 rounded-md hover:bg-blue-500 ">
                <span className="text-lg">
                  <i className="fas fa-plus-circle pr-1"></i>
                </span>
                <span>Transaction</span>
              </button>
              </div>
      </nav>

      {/* Table */}
      <div className="pt-20 mx-[-35px]">
        <div className="bg-white shadow-lg rounded-lg border overflow-hidden">
          <div className="overflow-x-auto">
            {/* <table className="w-full text-sm border-collapse"> */}
            <table className="w-full text-sm border-collapse">
              <colgroup>
                <col className="w-[300px]" /> {/* Part Name */}
                <col className="w-[150px]" /> {/* Date */}
                <col className="w-[120px]" /> {/* Voucher */}
                <col className="w-[180px]" /> {/* Stock Updated By */}
                <col className="w-[80px]" /> {/* Received Qty */}
                <col className="w-[100px]" /> {/* Received Rate */}
                <col className="w-[120px]" /> {/* Received Gross */}
                <col className="w-[80px]" /> {/* Issued Qty */}
                <col className="w-[100px]" /> {/* Issued Rate */}
                <col className="w-[120px]" /> {/* Issued Gross */}
                <col className="w-[150px]" /> {/* Available Quantity */}
              </colgroup>

              {/* Header */}
              <thead className="bg-gray-100 text-gray-700">
                <tr>
                  <th className="px-4 py-3 text-left border border-gray-300 font-bold W-50">
                    Part Name
                  </th>
                  <th className="px-4 py-3 text-left border border-gray-300 font-bold">
                    Date
                  </th>
                  <th className="px-4 py-3 text-left border border-gray-300 font-bold">
                    Voucher
                  </th>
                  <th className="px-4 py-3 text-left border border-gray-300 font-bold">
                    Stock Updated By
                  </th>
                  <th
                    className="px-4 py-3 text-center border border-gray-300 font-bold"
                    colSpan="3"
                  >
                    Received
                  </th>
                  <th
                    className="px-4 py-3 text-center border border-gray-300 font-bold"
                    colSpan="3"
                  >
                    Issued
                  </th>
                  <th className="px-4 py-3 text-center border border-gray-300 font-bold">
                    Available Quantity
                  </th>
                </tr>
                <tr className="bg-gray-50">
                  <th className="border border-gray-300"></th>
                  <th className="border border-gray-300"></th>
                  <th className="border border-gray-300"></th>
                  <th className="border border-gray-300"></th>
                  <th className="px-3 py-2 text-center border border-gray-300 text-xs font-bold- w-30">
                    Qty
                  </th>
                  <th className="px-3 py-2 text-center border border-gray-300 text-xs font-bold  w-30">
                    Rate
                  </th>
                  <th className="px-3 py-2 text-center border border-gray-300 text-xs font-bold w-30">
                    Gross
                  </th>
                  <th className="px-3 py-2 text-center border border-gray-300 text-xs font-bold w-30">
                    Qty
                  </th>
                  <th className="px-3 py-2 text-center border border-gray-300 text-xs font-bold 30">
                    Rate
                  </th>
                  <th className="px-3 py-2 text-center border border-gray-300 text-xs font-bold w-30">
                    Gross
                  </th>
                  <th className="border border-gray-300"></th>
                </tr>
              </thead>

              {/* Body */}
              <tbody>
                {inventoryData.map((row, index) => (
                  <tr key={index} className="hover:bg-gray-100">
                    <td className="px-4 py-3 border border-gray-300 font-medium">
                      {row.partName}
                    </td>
                    <td className="px-4 py-3 border border-gray-300 text-gray-600">
                      {row.date}
                    </td>
                    <td className="px-4 py-3 border border-gray-300">
                      {row.voucher}
                    </td>
                    <td className="px-4 py-3 border border-gray-300">
                      <div className="flex items-center gap-2">
                        <User className="w-4 h-4 text-gray-600" />
                        <span>{row.updatedBy}</span>
                      </div>
                    </td>

                    {/* Received columns */}
                    <td className="px-4 py-3 text-center border border-gray-300">
                      {row.received.qty}
                    </td>
                    <td className="px-4 py-3 text-center border border-gray-300">
                      {row.received.rate}
                    </td>
                    <td className="px-4 py-3 text-center border border-gray-300">
                      {row.received.gross}
                    </td>

                    {/* Issued columns */}
                    <td className="px-4 py-3 text-center border border-gray-300">
                      {row.issued.qty}
                    </td>
                    <td className="px-4 py-3 text-center border border-gray-300">
                      {row.issued.rate}
                    </td>
                    <td className="px-4 py-3 text-center border border-gray-300">
                      {row.issued.gross}
                    </td>

                    {/* Available quantity */}
                    <td className="px-4 py-3 text-center border border-gray-300 font-semibold">
                      {row.available}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EquipmentManagement;
