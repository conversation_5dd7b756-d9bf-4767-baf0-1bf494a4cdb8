// import React from "react";
// import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
// import Layout from "./components/Layout";
// import Dashboard from "./components/Dashboard";
// import InventoryManagement from "./components/InventoryManagement";
// import Login from "./components/LoginPage";
// import Assets from "./parts/Assets";
// import Inventory from "./parts/Inventory";
// import FacilityManagement from "./parts/FacilityManagement";
// import EquipmentManagement from "./parts/EquipmentManagement";
// import CreateInventory from "./parts/CreateInventory";
// import UpdateInventory from "./parts/UpdateInventory";
// import ActivityLogs from "./parts/ActivityLogs";

// function App() {
//   return (
//     <Router>
//       <Routes>
//         <Route path="/" element={<Layout />}>
//           <Route index element={<Dashboard />} />

//           {/* Nested Routes under InventoryManagement */}
//           {/* <Route path="inventory-management" element={<InventoryManagement />}>
//             <Route path="assets" element={<Assets />} />
//             <Route path="inventory" element={<Inventory />} />
//           </Route> */}
//           <Route path="inventory-management" element={<InventoryManagement />}>
//             {/* Assets Section */}
//             <Route path="assets" element={<Assets />}>
//               <Route path="facility" element={<FacilityManagement />} />
//               <Route path="equipment" element={<EquipmentManagement />} />
//             </Route>

//             {/* Inventory Section */}
//             <Route path="inventory" element={<Inventory />}>
//               <Route path="create" element={<CreateInventory />} />
//               <Route path="update" element={<UpdateInventory />} />
//               <Route path="logs" element={<ActivityLogs />} />
//             </Route>
//           </Route>

//           {/* Login route outside Layout */}
//           <Route path="/login" element={<Login />} />
//         </Route>
//       </Routes>
//     </Router>
//   );
// }

// export default App;

import React from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import Layout from "./components/Layout";
import Dashboard from "./components/Dashboard";
import InventoryManagement from "./components/InventoryManagement";
import Login from "./components/LoginPage";
import Assets from "./parts/Assets";
import Inventory from "./parts/Inventory";
import FacilityManagement from "./parts/FacilityManagement";
import EquipmentManagement from "./parts/EquipmentManagement";
import CreateInventory from "./parts/CreateInventory";
import UpdateInventory from "./parts/UpdateInventory";
import WorkflowAutomation from "./components/WorkflowAutomation";
import Workflows from "./parts/Workflows";

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<Dashboard />} />

          {/* Inventory Management */}
          <Route path="inventory-management" element={<InventoryManagement />}>
            {/* Assets Section */}
            <Route path="assets" element={<Assets />}>
              <Route path="facility" element={<FacilityManagement />} />
              <Route path="equipment" element={<EquipmentManagement />} />
            </Route>

            {/* Inventory Section */}
            <Route path="inventory" element={<Inventory />}>
              <Route path="create" element={<CreateInventory />} />
              <Route path="update" element={<UpdateInventory />} />
            </Route>
          </Route>

          <Route path="workflow-automation" element={<WorkflowAutomation />}>
            <Route path="workflows" element={<Workflows />} />
          </Route>
        </Route>

        {/* Login route outside Layout */}
        <Route path="/login" element={<Login />} />
      </Routes>
    </Router>
  );
}

export default App;
